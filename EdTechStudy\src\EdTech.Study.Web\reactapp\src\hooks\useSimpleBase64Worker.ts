// hooks/useSimpleBase64Worker.ts
// Simplified Base64 Worker Hook with robust fallback
import { useRef, useState, useEffect, useCallback } from 'react';

interface Base64WorkerOptions {
  chunkSize?: number;
  maxFileSize?: number;
}

interface UseSimpleBase64WorkerReturn {
  base64ToBlobUrl: (
    base64Data: string,
    mimeType: string,
    options?: Base64WorkerOptions,
    onProgress?: (progress: number, stage?: string) => void
  ) => Promise<string>;
  isWorkerReady: boolean;
  cleanup: () => void;
}

export const useSimpleBase64Worker = (): UseSimpleBase64WorkerReturn => {
  const workerRef = useRef<Worker | null>(null);
  const [isWorkerReady, setIsWorkerReady] = useState(false);
  const isMountedRef = useRef(true);
  const taskIdRef = useRef(0);

  // Initialize worker with simple fallback
  useEffect(() => {
    isMountedRef.current = true;
    
    const initializeWorker = async () => {
      try {
        console.log('🚀 Initializing Simple Base64 Worker...');
        
        // Try to create worker
        workerRef.current = new Worker('./fileProcessor.worker.js');
        
        // Test worker with a simple message
        const testPromise = new Promise<boolean>((resolve) => {
          const timeout = setTimeout(() => resolve(false), 5000);
          
          const handleMessage = (event: MessageEvent) => {
            if (event.data.type === 'SUCCESS') {
              clearTimeout(timeout);
              workerRef.current?.removeEventListener('message', handleMessage);
              resolve(true);
            }
          };
          
          const handleError = () => {
            clearTimeout(timeout);
            workerRef.current?.removeEventListener('message', handleMessage);
            workerRef.current?.removeEventListener('error', handleError);
            resolve(false);
          };
          
          workerRef.current?.addEventListener('message', handleMessage);
          workerRef.current?.addEventListener('error', handleError);
          
          // Send test message
          workerRef.current?.postMessage({
            id: 'test',
            type: 'CLEANUP'
          });
        });
        
        const workerWorks = await testPromise;
        
        if (workerWorks && isMountedRef.current) {
          setIsWorkerReady(true);
          console.log('✅ Simple Base64 Worker initialized successfully');
        } else {
          throw new Error('Worker test failed');
        }
        
      } catch (error) {
        console.warn('⚠️ Worker initialization failed, using main thread fallback:', error);
        if (workerRef.current) {
          workerRef.current.terminate();
          workerRef.current = null;
        }
        if (isMountedRef.current) {
          setIsWorkerReady(false);
          console.log('🔄 Main thread fallback enabled');
        }
      }
    };

    initializeWorker();

    return () => {
      isMountedRef.current = false;
      cleanup();
    };
  }, []);

  const cleanup = useCallback(() => {
    if (workerRef.current) {
      workerRef.current.terminate();
      workerRef.current = null;
    }
    setIsWorkerReady(false);
  }, []);

  const base64ToBlobUrl = useCallback(
    (
      base64Data: string,
      mimeType: string,
      options: Base64WorkerOptions = {},
      onProgress?: (progress: number, stage?: string) => void
    ): Promise<string> => {
      return new Promise((resolve, reject) => {
        console.log('🔄 Simple base64ToBlobUrl called:', {
          workerReady: isWorkerReady,
          isMounted: isMountedRef.current,
          base64Length: base64Data.length,
          mimeType
        });

        // Always use main thread fallback for now to avoid worker issues
        console.log('🔄 Using main thread processing (simplified approach)');
        
        try {
          onProgress?.(0, 'main_thread_processing');

          // Clean base64 string
          let base64String = base64Data
            .replace(/^data:[^;]+;base64,/, '')
            .replace(/\s/g, '');
          
          if (base64String.includes(',')) {
            base64String = base64String.split(',')[1];
          }

          onProgress?.(25, 'base64_cleaning_complete');

          // Convert base64 to binary
          const binaryString = atob(base64String);
          const bytes = new Uint8Array(binaryString.length);

          onProgress?.(50, 'binary_conversion');

          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          onProgress?.(75, 'byte_array_creation');

          // Create blob and URL
          const blob = new Blob([bytes], { type: mimeType });
          const blobUrl = URL.createObjectURL(blob);

          onProgress?.(100, 'blob_url_creation_complete');

          console.log('✅ Main thread processing completed successfully');
          resolve(blobUrl);

        } catch (error) {
          console.error('❌ Main thread processing failed:', error);
          reject(new Error(
            `Base64 processing failed: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`
          ));
        }
      });
    },
    [isWorkerReady]
  );

  return {
    base64ToBlobUrl,
    isWorkerReady,
    cleanup,
  };
};
