const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const fs = require('fs');
const webpack = require('webpack');

// Function to recursively delete directory contents
const cleanDirectory = (directory) => {
  if (fs.existsSync(directory)) {
    console.log(`Cleaning directory: ${directory}`);
    fs.rmSync(directory, { recursive: true, force: true });
    fs.mkdirSync(directory, { recursive: true });
    console.log(`Directory cleaned: ${directory}`);
  }
};

// Function to ensure worker directories exist
const ensureWorkerDirectories = (outputPath) => {
  const workerDir = path.join(outputPath, 'workers');
  if (!fs.existsSync(workerDir)) {
    fs.mkdirSync(workerDir, { recursive: true });
    console.log(`Created worker directory: ${workerDir}`);
  }
};

module.exports = (env) => {
  // Environment variables
  const envVars = {
    NODE_ENV: env.production ? 'production' : 'development',
  };

  // Common paths
  const paths = {
    src: path.resolve(__dirname, 'src'),
    public: path.resolve(__dirname, 'public'),
    workers: path.resolve(__dirname, 'src/workers'),
    output: env.production
      ? path.resolve(__dirname, '../wwwroot/EdTech/reactapp')
      : path.resolve(__dirname, 'dist'),
  };

  // Pages configuration - cập nhật theo vite.config.ts
  const pages = [
    {
      name: 'DemoLessonPage',
      entry: './src/pages/DemoLessonPage.tsx',
      template: 'public/demo.html',
      filename: 'demo.html',
    },
    {
      name: 'PreviewLessonPage',
      entry: './src/pages/PreviewLessonPage.tsx',
      template: 'public/preview.html',
      filename: 'preview.html',
    },
    {
      name: 'QuestionPage',
      entry: './src/pages/QuestionPage.tsx',
      template: 'public/question.html',
      filename: 'question.html',
    },
    {
      name: 'IconStorePage',
      entry: './src/pages/IconStorePage.tsx',
      template: 'public/iconStore.html',
      filename: 'iconStore.html',
    },
  ];

  // Generate entries and HTML plugins from pages configuration
  const { entries, htmlPlugins } = pages.reduce(
    (acc, page) => ({
      entries: { ...acc.entries, [page.name]: page.entry },
      htmlPlugins: [
        ...acc.htmlPlugins,
        new HtmlWebpackPlugin({
          title: 'index',
          template: env.production ? 'public/index.html' : page.template,
          filename: page.filename,
          chunks: [page.name],
          templateParameters: {
            idRoot: page.name,
          },
        }),
      ],
    }),
    { entries: {}, htmlPlugins: [] }
  );

  // Module rules configuration
  const moduleRules = [
    {
      test: /\.(js|jsx)$/,
      exclude: /node_modules/,
      use: ['babel-loader'],
    },
    {
      test: /\.(ts|tsx)$/,
      exclude: /node_modules/,
      use: [
        {
          loader: 'ts-loader',
          options: {
            // Bỏ qua lỗi TypeScript khi build
            transpileOnly: true,
            // Tắt kiểm tra lỗi
            compilerOptions: {
              noUnusedLocals: false,
              noUnusedParameters: false,
            },
          },
        },
      ],
    },
    {
      test: /\.(css|scss)$/,
      use: [MiniCssExtractPlugin.loader, 'css-loader', 'postcss-loader'],
    },
    {
      test: /\.(jpg|jpeg|png|gif|svg)$/,
      type: 'asset/resource',
      generator: {
        filename: 'assets/images/[name][ext]',
      },
    },
    {
      test: /\.(mp3)$/,
      use: ['file-loader'],
    },
  ];

  // Resolve configuration
  const resolveConfig = {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      // Thêm alias cho process/browser
      'process/browser': require.resolve('process/browser.js'),
    },
    fallback: {
      fs: false,
      buffer: require.resolve('buffer/'),
      stream: require.resolve('stream-browserify'),
      util: require.resolve('util/'),
      process: require.resolve('process/browser.js'),
    },
  };

  // Output configuration
  const outputConfig = {
    path: paths.output,
    filename: '[name].js',
    chunkFilename: '[name].js?_v=[chunkhash]',
    publicPath: env.production ? './EdTech/reactapp/' : '/',
    // ✅ Clean output before each build
    clean: true,
  };

  // DevServer configuration
  const devServerConfig = {
    static: [
      {
        directory: paths.public,
        publicPath: '/',
      },
      {
        directory: paths.output,
        publicPath: '/',
      },
    ],
    port: 8080,
    open: '/demo.html',
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
      logging: 'error',
    },
    // ✅ Headers for worker files
    headers: {
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin',
    },
    // ✅ Setup middleware for worker files
    setupMiddlewares: (middlewares, devServer) => {
      if (!devServer) {
        throw new Error('webpack-dev-server is not defined');
      }

      // Middleware to serve worker files with correct MIME type
      devServer.app.get('/workers/*.worker.js', (req, res, next) => {
        res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Access-Control-Allow-Origin', '*');
        next();
      });

      return middlewares;
    },
  };

  // Clean output directories before build
  if (env.production) {
    const prodOutputDir = path.resolve(__dirname, '../wwwroot/EdTech/reactapp');
    cleanDirectory(prodOutputDir);
    ensureWorkerDirectories(prodOutputDir);
  } else {
    const devOutputDir = path.resolve(__dirname, 'dist');
    cleanDirectory(devOutputDir);
    ensureWorkerDirectories(devOutputDir);
  }

  // ✅ Worker copy configuration - Simplified approach
  const getWorkerCopyPatterns = () => {
    const patterns = [];

    // Copy any static worker files from public/workers
    const publicWorkersPath = path.join(paths.public, 'workers');
    if (fs.existsSync(publicWorkersPath)) {
      patterns.push({
        from: publicWorkersPath,
        to: 'workers',
        globOptions: {
          ignore: ['**/.DS_Store', '**/Thumbs.db'],
        },
      });
      console.log('📦 Will copy static workers from public/workers');
    }

    // ✅ New: Copy compiled TypeScript workers
    const srcWorkersPath = path.join(paths.src, 'workers');
    if (fs.existsSync(srcWorkersPath)) {
      // Find TypeScript worker files
      const tsWorkerFiles = fs
        .readdirSync(srcWorkersPath)
        .filter((file) => file.endsWith('.worker.ts'));

      if (tsWorkerFiles.length > 0) {
        console.log(
          `📦 Found ${tsWorkerFiles.length} TypeScript workers to compile`
        );

        // We'll handle TypeScript workers with a separate webpack build
        // For now, just log them
        tsWorkerFiles.forEach((file) => {
          console.log(`   - ${file}`);
        });
      }
    }

    return patterns;
  };

  // Plugins configuration
  const plugins = [
    ...htmlPlugins,
    new NodePolyfillPlugin(),

    // ✅ Copy static worker files
    ...(getWorkerCopyPatterns().length > 0
      ? [new CopyWebpackPlugin({ patterns: getWorkerCopyPatterns() })]
      : []),

    // CSS extraction plugin - outputs two CSS files
    new MiniCssExtractPlugin({
      filename: env.production ? 'assets/css/[name].css' : '[name].css',
      chunkFilename: env.production
        ? 'assets/css/[name].css'
        : '[name].chunk.css',
    }),

    // Thêm polyfill cho Buffer và process
    new webpack.ProvidePlugin({
      Buffer: ['buffer', 'Buffer'],
      process: 'process/browser.js',
    }),

    // Thêm DefinePlugin để cung cấp process.env
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(envVars.NODE_ENV),
      'process.env': JSON.stringify({
        NODE_ENV: envVars.NODE_ENV,
        ...process.env,
      }),
    }),

    new webpack.DefinePlugin({
      'import.meta.env.MODE': JSON.stringify(envVars.NODE_ENV),
      'import.meta.env.DEV': JSON.stringify(!env.production),
      'import.meta.env.PROD': JSON.stringify(env.production),
    }),

    // ✅ Custom plugin to handle TypeScript workers
    {
      apply: (compiler) => {
        compiler.hooks.beforeCompile.tapAsync(
          'TypeScriptWorkerPlugin',
          (params, callback) => {
            // Compile TypeScript workers to JavaScript before main compilation
            const srcWorkersPath = path.join(paths.src, 'workers');
            const outputWorkersPath = path.join(paths.output, 'workers');

            if (fs.existsSync(srcWorkersPath)) {
              const tsWorkerFiles = fs
                .readdirSync(srcWorkersPath)
                .filter((file) => file.endsWith('.worker.ts'));

              if (tsWorkerFiles.length > 0) {
                console.log('🔧 Compiling TypeScript workers...');

                // Ensure output directory exists
                if (!fs.existsSync(outputWorkersPath)) {
                  fs.mkdirSync(outputWorkersPath, { recursive: true });
                }

                // Simple TypeScript compilation for workers
                const typescript = require('typescript');

                tsWorkerFiles.forEach((file) => {
                  const tsFilePath = path.join(srcWorkersPath, file);
                  const jsFileName = file.replace('.worker.ts', '.worker.js');
                  const jsFilePath = path.join(outputWorkersPath, jsFileName);

                  try {
                    const tsContent = fs.readFileSync(tsFilePath, 'utf8');

                    // TypeScript compilation options for workers
                    const compilerOptions = {
                      target: typescript.ScriptTarget.ES2018,
                      module: typescript.ModuleKind.None,
                      lib: ['ES2018', 'WebWorker'],
                      strict: false,
                      skipLibCheck: true,
                      removeComments: true,
                    };

                    const result = typescript.transpile(
                      tsContent,
                      compilerOptions
                    );

                    // Write compiled JavaScript
                    fs.writeFileSync(jsFilePath, result);
                    console.log(`✅ Compiled: ${file} -> ${jsFileName}`);
                  } catch (error) {
                    console.error(
                      `❌ Failed to compile ${file}:`,
                      error.message
                    );
                  }
                });
              }
            }

            callback();
          }
        );

        compiler.hooks.afterEmit.tap('WorkerInfoPlugin', (compilation) => {
          const workerAssets = Object.keys(compilation.assets).filter(
            (name) => name.includes('worker') && name.endsWith('.js')
          );

          // Also check for copied worker files
          const outputWorkersPath = path.join(paths.output, 'workers');
          if (fs.existsSync(outputWorkersPath)) {
            const copiedWorkers = fs
              .readdirSync(outputWorkersPath)
              .filter((name) => name.endsWith('.worker.js'));

            if (copiedWorkers.length > 0) {
              console.log('🟢 Worker files available:');
              copiedWorkers.forEach((worker) => {
                console.log(`   ✅ workers/${worker}`);
              });
            }
          }

          if (workerAssets.length > 0) {
            console.log('🟢 Worker assets generated:');
            workerAssets.forEach((asset) => {
              console.log(`   ✅ ${asset}`);
            });
          }
        });
      },
    },
  ];

  return {
    mode: envVars.NODE_ENV,
    entry: entries, // ✅ Only main app entries, workers handled separately
    module: {
      rules: moduleRules,
    },
    resolve: resolveConfig,
    plugins,
    devtool: env.production ? 'source-map' : 'eval-source-map',
    devServer: devServerConfig,
    output: outputConfig,

    // ✅ Standard optimization without worker complexity
    optimization: {
      runtimeChunk: 'single',
      splitChunks: {
        cacheGroups: {
          // Extract vendor CSS into a separate file
          vendorStyles: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/].*\.(css|scss)$/,
            chunks: 'all',
            enforce: true,
            priority: 20,
          },

          // Extract application CSS into a separate file
          appStyles: {
            name: 'app',
            test: /(?<!node_modules)[\\/].*\.(css|scss)$/,
            chunks: 'all',
            enforce: true,
            priority: 10,
          },
        },
      },
    },

    // Bỏ qua các cảnh báo khi build và khi chạy dev server
    ignoreWarnings: [
      /export .* was not found in/,
      /module .* should be in the dependencies/,
      /.*export 'default'.*was not found.*/,
      /\[mini-css-extract-plugin\]/,
      /Conflicting order/,
      /size limit/,
      /entrypoint size limit/,
      /webpack performance recommendations/,
      /asset size limit/,
      /The following asset\(s\) exceed the recommended size limit/,
      /You can limit the size of your bundles by using import\(\)/,
      // ✅ Worker-related warnings
      /webworker/,
      /worker.*chunk/,
    ],

    // Cấu hình stats - chỉ hiển thị lỗi, bỏ qua cảnh báo
    stats: {
      errorDetails: true,
      warnings: false,
      colors: true,
      assets: true, // Show assets to see worker files
      modules: false,
      performance: false,
      chunks: false,
      chunkModules: false,
      entrypoints: true,
    },
  };
};
