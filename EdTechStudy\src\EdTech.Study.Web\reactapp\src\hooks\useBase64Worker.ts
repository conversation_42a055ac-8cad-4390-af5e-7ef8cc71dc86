// hooks/useBase64Worker.ts
import { useRef, useState, useEffect, useCallback } from 'react';

interface WorkerTask {
  id: string;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  onProgress?: (
    progress: number,
    stage?: string,
    extra?: Record<string, any>
  ) => void;
}

interface Base64WorkerOptions {
  chunkSize?: number;
  maxFileSize?: number;
}

interface UseBase64WorkerReturn {
  base64ToBlobUrl: (
    base64Data: string,
    mimeType: string,
    options?: Base64WorkerOptions,
    onProgress?: (
      progress: number,
      stage?: string,
      extra?: Record<string, any>
    ) => void
  ) => Promise<string>;
  validateBase64: (
    base64Data: string
  ) => Promise<{ isValid: boolean; cleanedLength: number }>;
  isWorkerReady: boolean;
  cleanup: () => void;
}

export const useBase64Worker = (): UseBase64WorkerReturn => {
  const workerRef = useRef<Worker | null>(null);
  const tasksRef = useRef<Map<string, WorkerTask>>(new Map());
  const [isWorkerReady, setIsWorkerReady] = useState(false);
  const isMountedRef = useRef(true);

  // Initialize worker
  useEffect(() => {
    isMountedRef.current = true;
    const isFileProtocol = window.location.protocol === 'file:';

    try {
      if (isFileProtocol) {
        createInlineWorker();
      } else {
        createStandardWorker();
      }

      setupWorkerHandlers();
      if (isMountedRef.current) {
        setIsWorkerReady(true);
      }
    } catch (error) {
      console.warn(
        'Base64 Worker creation failed, using main thread fallback:',
        error
      );
      if (isMountedRef.current) {
        setIsWorkerReady(false);
      }
    }

    return () => {
      isMountedRef.current = false;
      cleanup();
    };
  }, []);

  const createStandardWorker = () => {
    workerRef.current = new Worker('./fileProcessor.worker.js');
  };

  const createInlineWorker = () => {
    // Inline worker code for file:// protocol
    const workerCode = `
      // Inline Base64 Processing Worker
      self.addEventListener('message', async (event) => {
        const { id, type, base64Data, mimeType, options = {} } = event.data;

        try {
          if (type === 'BASE64_TO_UINT8ARRAY') {
            await processBase64ToUint8Array(id, base64Data, mimeType, options);
          } else if (type === 'VALIDATE_BASE64') {
            validateBase64(id, base64Data);
          }
        } catch (error) {
          self.postMessage({
            id,
            type: 'ERROR',
            error: error.message
          });
        }
      });

      async function processBase64ToUint8Array(id, base64Data, mimeType, options) {
        try {
          // Clean base64 string
          let base64String = base64Data.replace(/^data:[^;]+;base64,/, '');
          base64String = base64String.replace(/\\s/g, '');
          if (base64String.includes(',')) {
            base64String = base64String.split(',')[1];
          }

          // Validate
          const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
          if (!base64Regex.test(base64String) || base64String.length % 4 !== 0) {
            throw new Error('Invalid base64 string format');
          }

          const chunkSize = options.chunkSize || 1024 * 1024;
          const base64ChunkSize = Math.floor(chunkSize * 4 / 3);
          const totalChunks = Math.ceil(base64String.length / base64ChunkSize);
          const uint8Chunks = [];

          for (let i = 0; i < totalChunks; i++) {
            const start = i * base64ChunkSize;
            const end = Math.min(start + base64ChunkSize, base64String.length);
            let base64Chunk = base64String.slice(start, end);

            // Ensure proper padding
            const missingPadding = base64Chunk.length % 4;
            if (missingPadding > 0) {
              base64Chunk += '='.repeat(4 - missingPadding);
            }

            const binaryString = atob(base64Chunk);
            const bytes = new Uint8Array(binaryString.length);

            for (let j = 0; j < binaryString.length; j++) {
              bytes[j] = binaryString.charCodeAt(j);
            }

            uint8Chunks.push(bytes);

            const progress = ((i + 1) / totalChunks) * 100;
            self.postMessage({
              id,
              type: 'PROGRESS',
              progress,
              stage: 'base64_decoding'
            });

            if (i % 10 === 0) {
              await new Promise(resolve => setTimeout(resolve, 0));
            }
          }

          const totalLength = uint8Chunks.reduce((sum, chunk) => sum + chunk.length, 0);
          const finalBytes = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of uint8Chunks) {
            finalBytes.set(chunk, offset);
            offset += chunk.length;
          }

          self.postMessage({
            id,
            type: 'SUCCESS',
            result: {
              uint8Array: finalBytes,
              mimeType,
              originalSize: base64Data.length,
              processedSize: finalBytes.length
            }
          });

        } catch (error) {
          self.postMessage({
            id,
            type: 'ERROR',
            error: 'Base64 processing failed: ' + error.message
          });
        }
      }

      function validateBase64(id, base64Data) {
        try {
          let cleanBase64 = base64Data.replace(/^data:[^;]+;base64,/, '').replace(/\\s/g, '');
          const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
          const isValid = base64Regex.test(cleanBase64) && cleanBase64.length % 4 === 0;

          self.postMessage({
            id,
            type: 'VALIDATION_RESULT',
            isValid,
            cleanedLength: cleanBase64.length
          });
        } catch (error) {
          self.postMessage({
            id,
            type: 'ERROR',
            error: 'Validation failed: ' + error.message
          });
        }
      }
    `;

    const blob = new Blob([workerCode], { type: 'application/javascript' });
    const workerUrl = URL.createObjectURL(blob);

    workerRef.current = new Worker(workerUrl);

    // Cleanup blob URL after worker is created
    setTimeout(() => URL.revokeObjectURL(workerUrl), 1000);
  };

  const setupWorkerHandlers = () => {
    if (!workerRef.current) return;

    workerRef.current.onmessage = (event) => {
      const {
        id,
        type,
        progress,
        result,
        error,
        stage,
        isValid,
        cleanedLength,
        ...extra
      } = event.data;
      const task = tasksRef.current.get(id);

      if (!task) return;

      switch (type) {
        case 'PROGRESS':
          task.onProgress?.(progress, stage, extra);
          break;

        case 'SUCCESS':
          tasksRef.current.delete(id);
          task.resolve(result);
          break;

        case 'VALIDATION_RESULT':
          tasksRef.current.delete(id);
          task.resolve({ isValid, cleanedLength });
          break;

        case 'ERROR':
          tasksRef.current.delete(id);
          task.reject(new Error(error));
          break;
      }
    };

    workerRef.current.onerror = (error) => {
      console.error('Worker error:', error);
      if (isMountedRef.current) {
        setIsWorkerReady(false);
      }

      // Reject all pending tasks
      tasksRef.current.forEach((task) => {
        task.reject(new Error('Worker error occurred'));
      });
      tasksRef.current.clear();
    };
  };

  const cleanup = useCallback(() => {
    if (workerRef.current) {
      // Cancel all pending tasks
      tasksRef.current.forEach((task) => {
        task.reject(new Error('Worker terminated'));
      });
      tasksRef.current.clear();

      workerRef.current.terminate();
      workerRef.current = null;
    }
    setIsWorkerReady(false);
  }, []);

  const base64ToBlobUrl = useCallback(
    (
      base64Data: string,
      mimeType: string,
      options: Base64WorkerOptions = {},
      onProgress?: (
        progress: number,
        stage?: string,
        extra?: Record<string, any>
      ) => void
    ): Promise<string> => {
      return new Promise((resolve, reject) => {
        if (!workerRef.current || !isWorkerReady) {
          // Fallback to main thread processing
          return fallbackBase64ToBlobUrl(base64Data, mimeType, onProgress)
            .then(resolve)
            .catch(reject);
        }

        const id = `base64_task_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        tasksRef.current.set(id, {
          id,
          resolve: (result) => {
            try {
              // Check if component is still mounted before processing result
              if (!isMountedRef.current) {
                reject(
                  new Error(
                    'Component unmounted before base64 processing completed'
                  )
                );
                return;
              }

              // Create blob URL on main thread from Uint8Array
              const blob = new Blob([result.uint8Array], {
                type: result.mimeType,
              });
              const blobUrl = URL.createObjectURL(blob);
              resolve(blobUrl);
            } catch (error) {
              reject(
                new Error(
                  `Failed to create blob URL: ${
                    error instanceof Error ? error.message : 'Unknown error'
                  }`
                )
              );
            }
          },
          reject: (error) => {
            // Add mounting check to reject handler
            if (!isMountedRef.current) {
              reject(new Error('Component unmounted during base64 processing'));
            } else {
              reject(error);
            }
          },
          onProgress: (progress, stage, extra) => {
            // Only call onProgress if component is still mounted
            if (isMountedRef.current && onProgress) {
              onProgress(progress, stage, extra);
            }
          },
        });

        // Send task to worker
        workerRef.current.postMessage({
          id,
          type: 'BASE64_TO_UINT8ARRAY',
          base64Data,
          mimeType,
          options,
        });
      });
    },
    [isWorkerReady]
  );

  const validateBase64 = useCallback(
    (
      base64Data: string
    ): Promise<{ isValid: boolean; cleanedLength: number }> => {
      return new Promise((resolve, reject) => {
        if (!workerRef.current || !isWorkerReady) {
          // Simple validation on main thread
          try {
            const cleaned = base64Data
              .replace(/^data:[^;]+;base64,/, '')
              .replace(/\\s/g, '');
            const isValid =
              /^[A-Za-z0-9+/]*={0,2}$/.test(cleaned) &&
              cleaned.length % 4 === 0;
            resolve({ isValid, cleanedLength: cleaned.length });
          } catch (error) {
            reject(error);
          }
          return;
        }

        const id = `validation_task_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;

        tasksRef.current.set(id, {
          id,
          resolve,
          reject,
        });

        workerRef.current.postMessage({
          id,
          type: 'VALIDATE_BASE64',
          base64Data,
        });
      });
    },
    [isWorkerReady]
  );

  return {
    base64ToBlobUrl,
    validateBase64,
    isWorkerReady,
    cleanup,
  };
};

// Fallback processing on main thread
async function fallbackBase64ToBlobUrl(
  base64Data: string,
  mimeType: string,
  onProgress?: (progress: number, stage?: string) => void
): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      onProgress?.(0, 'main_thread_fallback');

      // Clean base64 string
      let base64String = base64Data
        .replace(/^data:[^;]+;base64,/, '')
        .replace(/\\s/g, '');
      if (base64String.includes(',')) {
        base64String = base64String.split(',')[1];
      }

      const chunkSize = 1024 * 1024; // 1MB chunks
      const totalChunks = Math.ceil(base64String.length / chunkSize);
      const uint8Chunks: Uint8Array[] = [];
      let currentChunk = 0;

      const processNextChunk = () => {
        if (currentChunk >= totalChunks) {
          // Combine chunks
          const totalLength = uint8Chunks.reduce(
            (sum, chunk) => sum + chunk.length,
            0
          );
          const finalBytes = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of uint8Chunks) {
            finalBytes.set(chunk, offset);
            offset += chunk.length;
          }

          const blob = new Blob([finalBytes], { type: mimeType });
          const blobUrl = URL.createObjectURL(blob);
          onProgress?.(100, 'conversion_complete');
          resolve(blobUrl);
          return;
        }

        const start = currentChunk * chunkSize;
        const end = Math.min(start + chunkSize, base64String.length);
        let base64Chunk = base64String.slice(start, end);

        // Ensure proper padding
        const missingPadding = base64Chunk.length % 4;
        if (missingPadding > 0) {
          base64Chunk += '='.repeat(4 - missingPadding);
        }

        try {
          const binaryString = atob(base64Chunk);
          const bytes = new Uint8Array(binaryString.length);

          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          uint8Chunks.push(bytes);
          currentChunk++;

          const progress = (currentChunk / totalChunks) * 100;
          onProgress?.(progress, 'main_thread_processing');

          // Schedule next chunk
          requestIdleCallback(processNextChunk);
        } catch (error) {
          reject(
            new Error(
              `Failed to process chunk: ${
                error instanceof Error ? error.message : 'Unknown error'
              }`
            )
          );
        }
      };

      processNextChunk();
    } catch (error) {
      reject(
        new Error(
          `Fallback processing failed: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        )
      );
    }
  });
}
